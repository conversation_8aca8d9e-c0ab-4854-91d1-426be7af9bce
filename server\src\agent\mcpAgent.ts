import { MCPAgent, MCPClient } from 'mcp-use';
import { ChatOpenAI } from '@langchain/openai';
import { getOpenAIApiKey } from '../lib/env';

/**
 * MCP Agent factory with safety defaults
 * Provides a factory pattern for creating MCPAgent instances with secure configurations
 */

export interface CreateAgentOptions {
  client: MCPClient;
  modelName?: string;
  maxSteps?: number;
  disallowedTools?: string[];
  useServerManager?: boolean;
}

/**
 * Create an MCP agent with safety defaults
 * @param options Configuration options for the agent
 * @returns Promise resolving to configured MCPAgent
 */
export async function createMcpAgent({
  client,
  modelName = 'gpt-4o',
  maxSteps = 10,
  disallowedTools = ['file_system', 'network'],
  useServerManager = false
}: CreateAgentOptions): Promise<MCPAgent> {
  try {
    // Get OpenAI API key from environment
    const openAIApiKey = getOpenAIApiKey();
    
    // Create OpenAI LLM instance
    const llm = new ChatOpenAI({
      modelName,
      openAIApiKey,
      temperature: 0.1, // Lower temperature for more consistent analysis
      maxTokens: 4000,   // Reasonable limit for responses
    });

    // Create MCPAgent with safety defaults
    const agent = new MCPAgent({
      llm,
      client,
      maxSteps,
      disallowedTools,
      useServerManager
    });

    return agent;
  } catch (error) {
    console.error('Failed to create MCP agent:', error);
    throw new Error(`MCP agent initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create an MCP agent with custom LLM
 * @param client MCP client instance
 * @param llm Custom LLM instance
 * @param options Additional agent options
 * @returns Promise resolving to configured MCPAgent
 */
export async function createMcpAgentWithLLM(
  client: MCPClient,
  llm: ChatOpenAI,
  options: Partial<CreateAgentOptions> = {}
): Promise<MCPAgent> {
  try {
    const {
      maxSteps = 10,
      disallowedTools = ['file_system', 'network'],
      useServerManager = false
    } = options;

    const agent = new MCPAgent({
      llm,
      client,
      maxSteps,
      disallowedTools,
      useServerManager
    });

    return agent;
  } catch (error) {
    console.error('Failed to create MCP agent with custom LLM:', error);
    throw new Error(`MCP agent initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
