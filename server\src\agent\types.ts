/**
 * Type definitions for the MCP Analysis Companion Agent API
 * Provides request/response types for agent endpoints with proper validation
 */

/**
 * Request payload for the agent run endpoint
 */
export interface AgentRunRequest {
  /** The natural language prompt for the agent to process */
  prompt: string;
  /** Optional configuration for the agent run */
  options?: {
    /** Maximum number of steps the agent can take (1-20) */
    maxSteps?: number;
    /** Custom model name to use (defaults to gpt-4o) */
    modelName?: string;
    /** Additional tools to disallow beyond the default safety list */
    additionalDisallowedTools?: string[];
  };
}

/**
 * Event types that can be streamed from the agent
 */
export type AgentEventType = 
  | 'token'        // Individual token from LLM response
  | 'tool_call'    // Agent is calling a tool
  | 'tool_result'  // Result from a tool call
  | 'error'        // Error occurred during processing
  | 'complete'     // Agent run completed successfully
  | 'step'         // Agent completed a reasoning step
  | 'thinking';    // Agent is processing/thinking

/**
 * Individual event in the agent stream
 */
export interface AgentEvent {
  /** Type of event */
  type: AgentEventType;
  /** Main message or content for the event */
  message?: string;
  /** Tool name if this is a tool-related event */
  tool?: string;
  /** Tool arguments if this is a tool call */
  toolArgs?: any;
  /** Tool result if this is a tool result */
  toolResult?: any;
  /** Error details if this is an error event */
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
  /** Request correlation ID for tracking */
  requestId: string;
  /** ISO timestamp when the event occurred */
  timestamp: string;
  /** Current step number (for step events) */
  stepNumber?: number;
  /** Total steps taken so far */
  totalSteps?: number;
}

/**
 * Response envelope for successful agent runs
 */
export interface AgentRunResponse {
  /** Request correlation ID */
  requestId: string;
  /** Final response from the agent */
  response: string;
  /** Total number of steps taken */
  totalSteps: number;
  /** Tools that were used during the run */
  toolsUsed: string[];
  /** Total processing time in milliseconds */
  processingTimeMs: number;
  /** Timestamp when the run completed */
  completedAt: string;
}

/**
 * Error response for failed agent runs
 */
export interface AgentErrorResponse {
  /** Request correlation ID */
  requestId: string;
  /** Error message */
  error: string;
  /** Error code for programmatic handling */
  code: string;
  /** Additional error details */
  details?: any;
  /** Timestamp when the error occurred */
  timestamp: string;
}

/**
 * Validation schema for agent run requests
 */
export function validateAgentRunRequest(body: any): AgentRunRequest {
  if (!body || typeof body !== 'object' || Array.isArray(body)) {
    throw new Error('Request body must be a valid JSON object');
  }

  if (!body.prompt || typeof body.prompt !== 'string' || body.prompt.trim().length === 0) {
    throw new Error('Prompt is required and must be a non-empty string');
  }

  if (body.prompt.length > 10000) {
    throw new Error('Prompt must be less than 10,000 characters');
  }

  const request: AgentRunRequest = {
    prompt: body.prompt.trim()
  };

  if (body.options) {
    request.options = {};

    if (body.options.maxSteps !== undefined) {
      if (typeof body.options.maxSteps !== 'number' || body.options.maxSteps < 1 || body.options.maxSteps > 20) {
        throw new Error('maxSteps must be a number between 1 and 20');
      }
      request.options.maxSteps = body.options.maxSteps;
    }

    if (body.options.modelName !== undefined) {
      if (typeof body.options.modelName !== 'string' || body.options.modelName.trim().length === 0) {
        throw new Error('modelName must be a non-empty string');
      }
      request.options.modelName = body.options.modelName.trim();
    }

    if (body.options.additionalDisallowedTools !== undefined) {
      if (!Array.isArray(body.options.additionalDisallowedTools)) {
        throw new Error('additionalDisallowedTools must be an array');
      }
      request.options.additionalDisallowedTools = body.options.additionalDisallowedTools.filter(
        (tool: any) => typeof tool === 'string' && tool.trim().length > 0
      );
    }
  }

  return request;
}
