## Product Brief: Analysis Companion AI Agent (Multi‑MCP via mcp-use)

### 1) Project overview / description
Build a reusable "analysis companion" AI agent that orchestrates multiple MCP tools using `mcp-use`. The agent connects to remote MCP servers (initially `hustle-http` via `mcp-remote`) and exposes a streaming server API for UI consumption. It emphasizes safe‑by‑default tool access, robust observability, and easy extensibility without altering existing app behavior.

### 2) Target audience
- **Internal developers**: Implement and extend multi‑tool AI agents in the monorepo.
- **Analysts/ops engineers**: Run structured analyses via approved tools with auditability.
- **Product engineers (UI)**: Integrate streaming model/tool outputs into the existing React app.

### 3) Primary benefits / features
- **Multi‑MCP orchestration**: Connect to `hustle-http` via `mcp-remote`; simple addition of more MCP servers via `mcp-config.json`.
- **Streaming outputs**: Server endpoint streams `agent.streamEvents(prompt)` for responsive UI.
- **Safety by default**: Denylist high‑risk tools (default `file_system`, `network`), allowlist models, validate inputs, HTTPS‑only remotes, fail‑closed on TLS/cert errors.
- **Observability**: Log MCP server registration, tool invocations, errors; include correlation/request IDs.
- **Extensible + non‑breaking**: Clean factory for agent/client creation; namespacing strategy for tool collisions; no DB changes; modular additions in `server/` and optional UI demo.
- **Developer experience**: Scripts to run a dev agent and a CLI for quick prompts; concise docs referencing deeper research.

### 4) High‑level tech / architecture
- **Runtime & language**: Node.js 22+, TypeScript.
- **Agent stack**: `mcp-use` (`MCPClient` loaded via `MCPClient.fromConfigFile`, `MCPAgent`), optional `useServerManager` when multiple servers are configured.
- **LLM**: OpenAI via LangChain.js (`@langchain/openai`), default model `gpt-4o`, configurable `maxSteps` (default 10).
- **Remote MCP**: `mcp-remote` bridging HTTPS/SSE to `hustle-http` endpoint.
- **Server API**: New POST route streams events (SSE or chunked text); ensures `client.closeAllSessions()` per request lifecycle; robust timeouts and structured error payloads.
- **UI (optional)**: React 18 + Vite demo component for streaming display.
- **Config & env**: `mcp-config.json` for MCP servers; `OPENAI_API_KEY` from environment.
- **Docs**: Minimal usage guide; deeper context in `docs/research.md`.


