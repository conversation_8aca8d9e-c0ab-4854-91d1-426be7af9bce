name: "MCP Analysis Companion AI Agent Implementation"
description: |

## Purpose
Implement a production-ready AI agent that orchestrates multiple MCP tools using `mcp-use` library. The agent connects to remote MCP servers and exposes a streaming server API for UI consumption, emphasizing safety, observability, and extensibility.

## Core Principles
1. **Context is King**: Include ALL necessary documentation, examples, and caveats
2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
3. **Information Dense**: Use keywords and patterns from the codebase
4. **Progressive Success**: Start simple, validate, then enhance
5. **Global rules**: Be sure to follow all rules in CLAUDE.md

---

## Goal
Build a reusable "analysis companion" AI agent that orchestrates multiple MCP tools using `mcp-use`. The agent connects to remote MCP servers (initially `hustle-http` via `mcp-remote`) and exposes a streaming server API for UI consumption. It emphasizes safe‑by‑default tool access, robust observability, and easy extensibility without altering existing app behavior.

## Why
- **Business value**: Enables AI-powered analysis capabilities for users through standardized MCP protocol
- **Integration**: Leverages existing Hono API infrastructure and authentication patterns
- **Problems solved**: Provides secure, scalable way to connect LLMs to external tools and data sources
- **Extensibility**: Foundation for adding multiple MCP servers and advanced agent capabilities

## What
A streaming AI agent API that:
- Accepts natural language prompts via POST endpoint
- Uses OpenAI GPT-4o to process requests with MCP tool access
- Streams real-time agent events and responses to clients
- Maintains secure tool restrictions and proper session lifecycle
- Integrates seamlessly with existing authentication and infrastructure

### Success Criteria
- [ ] Agent successfully connects to hustle-http MCP server via mcp-remote
- [ ] POST /api/v1/agent/run endpoint streams agent events in real-time
- [ ] Tool restrictions prevent access to file_system and network tools
- [ ] Proper session cleanup occurs on request completion/error
- [ ] Request correlation IDs enable observability and debugging
- [ ] Manual testing confirms streaming works with sample prompts
- [ ] Unit tests validate core functionality and error handling

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://github.com/mcp-use/mcp-use
  why: Primary library documentation, MCPClient and MCPAgent usage patterns
  
- url: https://github.com/modelcontextprotocol/typescript-sdk
  why: MCP protocol specification and TypeScript implementation details
  
- file: server/src/api.ts
  why: Existing Hono routing patterns, middleware usage, protected routes structure
  
- file: server/src/middleware/auth.ts
  why: Authentication middleware patterns for protected endpoints
  
- file: server/src/lib/env.ts
  why: Environment variable utilities and cross-platform compatibility patterns
  
- file: docs/features/0001_PLAN.md
  why: Detailed implementation requirements and technical specifications
  
- file: docs/research.md
  why: MCP library API references, best practices, and configuration examples
  
- file: server/package.json
  why: Existing dependencies including mcp-use and @langchain/openai
```

### Current Codebase tree
```bash
server/
├── src/
│   ├── api.ts                    # Main Hono API router with public/protected routes
│   ├── server.ts                 # Server entry point with CLI args parsing
│   ├── middleware/
│   │   └── auth.ts              # Firebase auth middleware
│   ├── lib/
│   │   ├── env.ts               # Cross-platform environment utilities
│   │   ├── db.ts                # Database connection utilities
│   │   └── firebase-auth.ts     # Firebase token verification
│   └── schema/
│       └── users.ts             # User database schema
├── package.json                  # Dependencies include mcp-use, @langchain/openai
└── README.md                    # Environment setup documentation
```

### Desired Codebase tree with files to be added
```bash
server/
├── mcp-config.json              # MCP servers configuration (single source of truth)
├── src/
│   ├── agent/
│   │   ├── mcpClient.ts         # MCPClient factory and session management
│   │   ├── mcpAgent.ts          # MCPAgent factory with safety defaults
│   │   └── types.ts             # Request/response types for agent endpoints
│   └── api.ts                   # Updated with /api/v1/agent/run endpoint
```

### Known Gotchas of our codebase & Library Quirks
```typescript
// CRITICAL: mcp-use requires Node.js ≥ 22 (per docs/research.md)
// Example: Ensure OPENAI_API_KEY is set in environment before agent creation
// Example: Always call client.closeAllSessions() in finally blocks to prevent resource leaks
// Example: Use disallowedTools: ['file_system','network'] for safety by default
// Example: Hono context variables must be properly typed for user access in protected routes
// Example: Environment utilities in server/src/lib/env.ts handle both Node.js and Cloudflare Workers
```

## Implementation Blueprint

### Data models and structure

Create the core data models to ensure type safety and consistency.
```typescript
// agent/types.ts - Request/response types for agent endpoints
interface AgentRunRequest {
  prompt: string;
  options?: {
    maxSteps?: number;
  };
}

interface AgentEvent {
  type: 'token' | 'tool_call' | 'tool_result' | 'error' | 'complete';
  message?: string;
  tool?: string;
  requestId: string;
  timestamp: string;
}
```

### List of tasks to be completed to fulfill the PRP in the order they should be completed

```yaml
Task 1: Create MCP Configuration File
CREATE server/mcp-config.json:
  - CONTENT: Baseline configuration with hustle-http remote server
  - PATTERN: JSON configuration as specified in docs/research.md
  - VALIDATE: Ensure HTTPS URL and proper mcp-remote command structure

Task 2: Add OPENAI_API_KEY to Environment Configuration
MODIFY server/src/lib/env.ts:
  - FIND pattern: "export function getFirebaseProjectId"
  - INJECT after: New function getOpenAIApiKey() with validation
  - PRESERVE: Existing environment utility patterns and error handling

Task 3: Create MCP Client Factory
CREATE server/src/agent/mcpClient.ts:
  - PATTERN: Factory pattern with resource cleanup
  - EXPOSE: createMcpClient() function returning initialized client
  - INCLUDE: closeAll() helper for session management
  - VALIDATE: Proper error handling and resource cleanup

Task 4: Create MCP Agent Factory
CREATE server/src/agent/mcpAgent.ts:
  - PATTERN: Factory pattern with safety defaults
  - EXPOSE: createMcpAgent({ client, modelName }) function
  - DEFAULTS: maxSteps: 10, disallowedTools: ['file_system','network']
  - INTEGRATE: OpenAI LLM from @langchain/openai

Task 5: Create Agent Types
CREATE server/src/agent/types.ts:
  - DEFINE: AgentRunRequest and AgentEvent interfaces
  - PATTERN: Follow existing type patterns in codebase
  - INCLUDE: Request validation schemas and response envelopes

Task 6: Add Agent API Endpoint
MODIFY server/src/api.ts:
  - FIND pattern: "const api = new Hono();"
  - INJECT after: New sub-router for /api/v1/agent with POST /run endpoint
  - PATTERN: Follow existing public route patterns initially
  - INCLUDE: Streaming response handler with proper cleanup

Task 7: Add Request Logging and Correlation IDs
MODIFY server/src/api.ts agent endpoint:
  - PATTERN: Generate UUID v4 for request correlation
  - LOG: Request start, completion, errors with requestId
  - SECURITY: Log prompt length only, never full prompt content
  - INCLUDE: Tool usage summary in completion logs

Task 8: Create Unit Tests
CREATE server/src/agent/__tests__/:
  - PATTERN: Follow vitest patterns from package.json test script
  - TEST: mcpClient factory, mcpAgent factory, types validation
  - MOCK: External dependencies (OpenAI, MCP servers)
  - VALIDATE: Error handling, resource cleanup, safety defaults

Task 9: Add Integration Test
CREATE server/src/agent/__tests__/integration.test.ts:
  - PATTERN: End-to-end test of agent endpoint
  - MOCK: MCP server responses and OpenAI completions
  - VALIDATE: Streaming response format and session cleanup
  - TEST: Error scenarios and timeout handling

Task 10: Manual Testing and Documentation
UPDATE server/README.md:
  - ADD: Agent endpoint documentation and usage examples
  - INCLUDE: Environment variable requirements (OPENAI_API_KEY)
  - DOCUMENT: Example curl commands for testing
  - VALIDATE: Manual testing with real prompts
```

### Per task pseudocode as needed added to each task

```typescript
// Task 3: MCP Client Factory pseudocode
export async function createMcpClient(): Promise<{ client: MCPClient, closeAll: () => Promise<void> }> {
  const configPath = path.join(process.cwd(), 'mcp-config.json');
  const client = MCPClient.fromConfigFile(configPath);

  return {
    client,
    closeAll: async () => await client.closeAllSessions()
  };
}

// Task 4: MCP Agent Factory pseudocode
export async function createMcpAgent({
  client,
  modelName = 'gpt-4o'
}: CreateAgentOptions): Promise<MCPAgent> {
  const llm = new ChatOpenAI({
    modelName,
    openAIApiKey: getOpenAIApiKey()
  });

  return new MCPAgent({
    llm,
    client,
    maxSteps: 10,
    disallowedTools: ['file_system', 'network']
  });
}

// Task 6: Streaming endpoint pseudocode
api.post('/agent/run', async (c) => {
  const requestId = randomUUID();
  const { prompt, options } = await c.req.json();

  try {
    const { client, closeAll } = await createMcpClient();
    const agent = await createMcpAgent({ client });

    return streamText(c, async function* () {
      for await (const event of agent.streamEvents(prompt, options)) {
        yield JSON.stringify({ ...event, requestId }) + '\n';
      }
    });
  } finally {
    await closeAll();
  }
});
```

### Integration Points
```yaml
ENVIRONMENT:
  - add to: server/src/lib/env.ts
  - pattern: "export function getOpenAIApiKey(): string"
  - validation: "getRequiredEnv('OPENAI_API_KEY')"

CONFIG:
  - add to: server/mcp-config.json
  - pattern: MCP servers configuration with hustle-http remote
  - location: "Co-located with server for simple deployment"

ROUTES:
  - add to: server/src/api.ts
  - pattern: "api.route('/agent', agentRouter)"
  - endpoint: "POST /api/v1/agent/run for streaming responses"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
cd server
npm run test -- --run --reporter=verbose  # Run vitest tests
npx tsc --noEmit                          # TypeScript compilation check

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests each new feature/file/function use existing test patterns
```typescript
// CREATE server/src/agent/__tests__/mcpClient.test.ts
import { describe, it, expect, vi } from 'vitest';
import { createMcpClient } from '../mcpClient';

describe('MCP Client Factory', () => {
  it('should create client from config file', async () => {
    const { client, closeAll } = await createMcpClient();
    expect(client).toBeDefined();
    expect(closeAll).toBeInstanceOf(Function);
  });

  it('should handle cleanup properly', async () => {
    const { closeAll } = await createMcpClient();
    await expect(closeAll()).resolves.not.toThrow();
  });
});

// CREATE server/src/agent/__tests__/mcpAgent.test.ts
describe('MCP Agent Factory', () => {
  it('should create agent with safety defaults', async () => {
    const mockClient = vi.fn();
    const agent = await createMcpAgent({ client: mockClient });
    expect(agent.disallowedTools).toContain('file_system');
    expect(agent.disallowedTools).toContain('network');
  });
});
```

### Level 3: Integration Test
```bash
# Test the agent endpoint manually
curl -X POST http://localhost:8787/api/v1/agent/run \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Hello, what tools do you have available?"}'

# Expected: Streaming response with agent events
# Response format: {"type":"token","message":"Hello!","requestId":"uuid","timestamp":"..."}
# If error: Check logs for stack trace and session cleanup
```

### Level 4: Manual Validation
```bash
# Test with environment variables set
export OPENAI_API_KEY="your-openai-api-key"
cd server && npm run dev

# Test streaming endpoint
curl -X POST http://localhost:8787/api/v1/agent/run \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Analyze the current state of AI development", "options": {"maxSteps": 5}}'

# Expected: Real-time streaming of agent analysis with tool usage
# Verify: Session cleanup occurs, no resource leaks in logs
```

## Final validation Checklist
- [ ] All tests pass: `cd server && npm run test`
- [ ] No TypeScript errors: `npx tsc --noEmit`
- [ ] Manual test successful: Agent responds to prompts with streaming
- [ ] Tool restrictions enforced: file_system and network tools blocked
- [ ] Session cleanup verified: No resource leak warnings in logs
- [ ] Request correlation IDs present in all log entries
- [ ] Error cases handled gracefully with proper HTTP status codes
- [ ] Documentation updated with agent endpoint usage

---

**PRP Confidence Score: 9/10**

This PRP provides comprehensive context including:
✅ Complete library documentation and examples
✅ Existing codebase patterns and integration points
✅ Step-by-step implementation tasks with pseudocode
✅ Executable validation commands for iterative development
✅ Security considerations and safety defaults
✅ Proper error handling and resource management patterns
✅ Real-world testing scenarios and expected outcomes

The implementation should succeed in one-pass with this level of detail and context.
