import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import app from '../../api';

// Mock all agent dependencies
vi.mock('../mcpClient', () => ({
  createMcpClient: vi.fn()
}));

vi.mock('../mcpAgent', () => ({
  createMcpAgent: vi.fn()
}));

vi.mock('crypto', () => ({
  randomUUID: vi.fn(() => 'test-request-id-123')
}));

describe('Agent API Integration Tests', () => {
  const mockRequestId = 'test-request-id-123';
  const mockClient = {
    closeAllSessions: vi.fn()
  };
  const mockCloseAll = vi.fn();
  const mockAgent = {
    streamEvents: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // UUID is already mocked to return the mockRequestId
    
    // Mock MCP client creation
    const { createMcpClient } = require('../mcpClient');
    createMcpClient.mockResolvedValue({
      client: mockClient,
      closeAll: mockCloseAll
    });
    
    // Mock MCP agent creation
    const { createMcpAgent } = require('../mcpAgent');
    createMcpAgent.mockResolvedValue(mockAgent);
    
    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('POST /api/v1/agent/run', () => {
    it('should handle successful agent run with streaming response', async () => {
      // Arrange
      const mockEvents = [
        { type: 'token', message: 'Hello', text: 'Hello' },
        { type: 'tool_call', tool: 'test_tool', toolArgs: { arg: 'value' } },
        { type: 'tool_result', tool: 'test_tool', toolResult: { result: 'success' } },
        { type: 'token', message: ' world!', text: ' world!' }
      ];
      
      mockAgent.streamEvents.mockImplementation(async function* () {
        for (const event of mockEvents) {
          yield event;
        }
      });

      const requestBody = {
        prompt: 'Hello, world!',
        options: {
          maxSteps: 5,
          modelName: 'gpt-4'
        }
      };

      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Assert
      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toBe('application/x-ndjson');
      expect(response.headers.get('Cache-Control')).toBe('no-cache');
      expect(response.headers.get('Connection')).toBe('keep-alive');

      // Verify MCP client and agent were created correctly
      const { createMcpClient } = require('../mcpClient');
      const { createMcpAgent } = require('../mcpAgent');
      
      expect(createMcpClient).toHaveBeenCalledOnce();
      expect(createMcpAgent).toHaveBeenCalledWith({
        client: mockClient,
        modelName: 'gpt-4',
        maxSteps: 5,
        disallowedTools: ['file_system', 'network']
      });

      // Verify agent was called with correct prompt
      expect(mockAgent.streamEvents).toHaveBeenCalledWith('Hello, world!');

      // Verify cleanup was called
      expect(mockCloseAll).toHaveBeenCalledOnce();

      // Verify logging
      expect(console.log).toHaveBeenCalledWith(
        `[${mockRequestId}] Agent request started`,
        expect.objectContaining({
          promptLength: 13,
          maxSteps: 5,
          modelName: 'gpt-4'
        })
      );
    });

    it('should handle minimal request with defaults', async () => {
      // Arrange
      mockAgent.streamEvents.mockImplementation(async function* () {
        yield { type: 'token', message: 'Response', text: 'Response' };
      });

      const requestBody = {
        prompt: 'Simple prompt'
      };

      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Assert
      expect(response.status).toBe(200);

      // Verify agent was created with defaults
      const { createMcpAgent } = require('../mcpAgent');
      expect(createMcpAgent).toHaveBeenCalledWith({
        client: mockClient,
        modelName: undefined,
        maxSteps: undefined,
        disallowedTools: ['file_system', 'network']
      });
    });

    it('should handle additional disallowed tools', async () => {
      // Arrange
      mockAgent.streamEvents.mockImplementation(async function* () {
        yield { type: 'token', message: 'Response', text: 'Response' };
      });

      const requestBody = {
        prompt: 'Test prompt',
        options: {
          additionalDisallowedTools: ['custom_tool', 'risky_tool']
        }
      };

      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Assert
      expect(response.status).toBe(200);

      // Verify additional tools were added to disallowed list
      const { createMcpAgent } = require('../mcpAgent');
      expect(createMcpAgent).toHaveBeenCalledWith({
        client: mockClient,
        modelName: undefined,
        maxSteps: undefined,
        disallowedTools: ['file_system', 'network', 'custom_tool', 'risky_tool']
      });
    });

    it('should handle validation errors', async () => {
      // Arrange
      const requestBody = {
        prompt: '', // Invalid empty prompt
      };

      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Assert
      expect(response.status).toBe(500);
      const responseBody = await response.json();
      expect(responseBody).toMatchObject({
        requestId: mockRequestId,
        error: 'Prompt is required and must be a non-empty string',
        code: 'AGENT_ERROR'
      });

      // Verify cleanup was not called since client wasn't created
      expect(mockCloseAll).not.toHaveBeenCalled();
    });

    it('should handle MCP client creation errors', async () => {
      // Arrange
      const { createMcpClient } = require('../mcpClient');
      createMcpClient.mockRejectedValue(new Error('MCP client failed to initialize'));

      const requestBody = {
        prompt: 'Test prompt'
      };

      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Assert
      expect(response.status).toBe(500);
      const responseBody = await response.json();
      expect(responseBody).toMatchObject({
        requestId: mockRequestId,
        error: 'MCP client failed to initialize',
        code: 'AGENT_ERROR'
      });
    });

    it('should handle MCP agent creation errors', async () => {
      // Arrange
      const { createMcpAgent } = require('../mcpAgent');
      createMcpAgent.mockRejectedValue(new Error('MCP agent failed to initialize'));

      const requestBody = {
        prompt: 'Test prompt'
      };

      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Assert
      expect(response.status).toBe(500);
      const responseBody = await response.json();
      expect(responseBody).toMatchObject({
        requestId: mockRequestId,
        error: 'MCP agent failed to initialize',
        code: 'AGENT_ERROR'
      });

      // Verify cleanup was still called
      expect(mockCloseAll).toHaveBeenCalledOnce();
    });

    it('should handle streaming errors gracefully', async () => {
      // Arrange
      mockAgent.streamEvents.mockImplementation(async function* () {
        yield { type: 'token', message: 'Start', text: 'Start' };
        throw new Error('Streaming failed');
      });

      const requestBody = {
        prompt: 'Test prompt'
      };

      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      // Assert
      expect(response.status).toBe(200); // Streaming starts successfully
      expect(response.headers.get('Content-Type')).toBe('application/x-ndjson');

      // Verify cleanup was called even after streaming error
      expect(mockCloseAll).toHaveBeenCalledOnce();
    });

    it('should handle invalid JSON in request body', async () => {
      // Act
      const response = await app.request('/api/v1/agent/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: 'invalid json'
      });

      // Assert
      expect(response.status).toBe(500);
      const responseBody = await response.json() as any;
      expect(responseBody).toMatchObject({
        requestId: mockRequestId,
        code: 'AGENT_ERROR'
      });
      expect(responseBody.error).toContain('JSON');
    });
  });
});
