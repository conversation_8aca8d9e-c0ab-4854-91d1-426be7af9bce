## Feature Plan: Initial Analysis Companion AI Agent (Multi‑MCP via mcp-use)

### Context
Build a reusable "analysis companion" AI agent that orchestrates multiple MCP tools using `mcp-use`. The agent connects to remote MCP servers (initially `hustle-http` via `mcp-remote`) and exposes a streaming server API for UI consumption. It emphasizes safe‑by‑default tool access, robust observability, and easy extensibility without altering existing app behavior. References: `docs/PRODUCT_BRIEF.md`, `docs/research.md`.

### Scope (initial version)
- Single OpenAI-backed agent using `mcp-use` that can call tools from a remote MCP (`hustle-http` via `mcp-remote`).
- One server endpoint that streams agent events (`streamEvents`) for consumption by the UI.
- Safe‑by‑default configuration with tool denylist and request-scoped teardown (`closeAllSessions`).
- Minimal logs and correlation IDs.
- No database changes.

### Dependencies to add (server)
- `mcp-use` (TypeScript client library)
- `@langchain/openai` (or equivalent OpenAI client used by `mcp-use` examples)

Note: Node.js ≥ 22 must be used (per `docs/research.md`). Ensure `OPENAI_API_KEY` is set in environment.

### Files to create/update
- Create `server/mcp-config.json`
  - Baseline content (from `docs/research.md`):
    - `mcpServers.hustle-http` → `npx mcp-remote https://agenthustle.myagent.sh/mcp`
  - This is the single source of truth for MCP servers; future servers can be appended.

- Create `server/src/agent/mcpClient.ts`
  - Responsibility: load and initialize an `MCPClient` from `mcp-config.json` via `MCPClient.fromConfigFile(path)`.
  - Expose: `createMcpClient()` that returns an initialized client and a `closeAll()` helper.

- Create `server/src/agent/mcpAgent.ts`
  - Responsibility: construct an `MCPAgent` instance given an `MCPClient` and an OpenAI LLM.
  - Defaults: `maxSteps: 10`, `disallowedTools: ['file_system','network']` (safety by default), optional `useServerManager` false for single server.
  - Expose: `createMcpAgent({ client, modelName = 'gpt-4o' })`.

- Update `server/src/api.ts`
  - Add a new `Hono` sub-router under `/api/v1/agent` with a POST endpoint `/run`.
  - The handler should stream text events (SSE or chunked text) from `agent.streamEvents(prompt)` to the response.
  - Ensure per-request lifecycle: on completion or error, invoke `client.closeAllSessions()`.
  - Add basic request/response logging with a request ID.

- Create `server/src/agent/types.ts`
  - Define request/response types for the endpoint: `{ prompt: string, options?: { maxSteps?: number } }` and a minimal event envelope when chunking JSON (if not using SSE).

- Optional UI demo (non-blocking for initial):
  - Add `ui/src/lib/serverComm.ts`: extend with `postAgentStream(prompt: string)` that streams from `/api/v1/agent/run` using `fetch` + `ReadableStream` reader. Reuse existing auth header logic if the route is protected; initial version can be public for easier testing.
  - Add `ui/src/components/agent-demo.tsx`: minimal component to send a prompt and render streamed text.

### Endpoint contract
- Method: `POST /api/v1/agent/run`
- Body: `{ prompt: string, options?: { maxSteps?: number } }`
- Auth: initially unauthenticated (toggleable). For production, protect with existing Firebase auth middleware.
- Response: server-sent events or newline-delimited JSON chunks, each representing an agent event or token output. Terminal event closes the stream.

### Step-by-step flow (algorithm)
1) Parse and validate input body; ensure `prompt` is a non-empty string; clamp `options.maxSteps` to a safe range (e.g., 1–20).
2) Generate a `requestId` and log: request start, client IP (if available), user id (if authenticated), prompt length only (never log full prompt with secrets).
3) Create `MCPClient` via `MCPClient.fromConfigFile('server/mcp-config.json')`.
4) Construct LLM (`ChatOpenAI`) with `modelName: 'gpt-4o'` and `OPENAI_API_KEY` from env.
5) Create `MCPAgent` with `{ llm, client, maxSteps, disallowedTools: ['file_system','network'] }`.
6) Call `agent.streamEvents(prompt)`.
   - For each event: map to SSE `data:` lines or NDJSON chunks; include minimal fields `{ type, message/text, tool? }`.
   - Flush progressively to the client for smooth streaming.
7) On completion or error: send a final event, end the stream, and `await client.closeAllSessions()`.
8) Log: success/failure with `requestId`, elapsed ms, tool summary (names/count), and any errors.

### Observability
- Add correlation IDs (UUID v4) per request.
- Log MCP server registration at app boot (once) and log tool invocation summaries per request.
- Ensure logs omit sensitive content (no secrets, no full tool inputs unless safe).

### Security & safety
- Denylist risky tools by default: `['file_system','network']` as per `docs/research.md` best practices.
- Only connect to HTTPS remotes. `mcp-remote` URL must be `https://`.
- Timeouts: set a per-request timeout (e.g., 60–90s) and abort controller for the agent run.
- Enforce size limits on input (prompt length) and streamed output where practical.
- Secrets strictly from environment (`OPENAI_API_KEY`); never serialize into responses or prompts.

### Configuration & environment
- Add `OPENAI_API_KEY` to server environment with existing env utilities in `server/src/lib/env.ts`.
- Place `mcp-config.json` in `server/` (co-located with the service) for simple deployment.
- Keep the endpoint public initially for testing; when stabilizing, mount under `/api/v1/protected/agent/run` and apply `authMiddleware`.

### Testing & validation
- Local dev: run server with Node 22. Use a simple CURL to POST a prompt and confirm streamed output (tokens and tool events appear).
- Add a trivial “smoke” test using `vitest` that mocks the agent and verifies streaming handler writes multiple chunks and closes.
- Manual test with the UI demo (if added) to confirm rendering and backpressure handling.

### Rollout steps
1) Add dependencies to `server` package and install.
2) Add `server/mcp-config.json` with the baseline remote.
3) Implement `mcpClient.ts` and `mcpAgent.ts` factories.
4) Add the `/api/v1/agent/run` route and streaming logic to `server/src/api.ts`.
5) Smoke test locally with a sample prompt.
6) Optionally: add UI streaming helper and demo component.
7) Harden: enable auth, add timeouts, and finalize logs.

### Future extensions (non-blocking)
- Add multiple MCP servers and enable `useServerManager` for dynamic routing.
- Namespacing strategy for overlapping tool names.
- Structured output mode for tools that return typed data.
- Persist minimal run metadata to DB for audit (not required for initial).


