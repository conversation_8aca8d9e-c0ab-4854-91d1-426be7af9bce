import { describe, it, expect } from 'vitest';
import { validateAgentRunRequest } from '../types';

describe('Agent Smoke Tests', () => {
  describe('Types validation', () => {
    it('should validate a basic request', () => {
      const request = validateAgentRunRequest({
        prompt: 'Hello, world!'
      });
      
      expect(request.prompt).toBe('Hello, world!');
    });

    it('should validate a request with options', () => {
      const request = validateAgentRunRequest({
        prompt: 'Test prompt',
        options: {
          maxSteps: 5,
          modelName: 'gpt-4'
        }
      });
      
      expect(request.prompt).toBe('Test prompt');
      expect(request.options?.maxSteps).toBe(5);
      expect(request.options?.modelName).toBe('gpt-4');
    });

    it('should throw error for invalid request', () => {
      expect(() => validateAgentRunRequest({})).toThrow('Prompt is required');
      expect(() => validateAgentRunRequest({ prompt: '' })).toThrow('Prompt is required');
      expect(() => validateAgentRunRequest(null)).toThrow('Request body must be a valid JSON object');
    });
  });

  describe('Module imports', () => {
    it('should import mcpClient module', async () => {
      const module = await import('../mcpClient');
      expect(module.createMcpClient).toBeDefined();
      expect(typeof module.createMcpClient).toBe('function');
    });

    it('should import mcpAgent module', async () => {
      const module = await import('../mcpAgent');
      expect(module.createMcpAgent).toBeDefined();
      expect(typeof module.createMcpAgent).toBe('function');
    });

    it('should import types module', async () => {
      const module = await import('../types');
      expect(module.validateAgentRunRequest).toBeDefined();
      expect(typeof module.validateAgentRunRequest).toBe('function');
    });
  });
});
