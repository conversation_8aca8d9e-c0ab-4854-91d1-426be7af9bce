import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createMcpClient, createMcpClientFromConfig } from '../mcpClient';
import { MCPClient } from 'mcp-use';
import path from 'path';

// Mock the mcp-use library
vi.mock('mcp-use', () => ({
  MCPClient: {
    fromConfigFile: vi.fn(),
    fromDict: vi.fn()
  }
}));

// Mock path module
vi.mock('path', () => ({
  default: {
    join: vi.fn()
  }
}));

describe('MCP Client Factory', () => {
  const mockClient = {
    closeAllSessions: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock process.cwd()
    vi.spyOn(process, 'cwd').mockReturnValue('/test/server');
    // Mock path.join
    (path.join as any).mockReturnValue('/test/server/mcp-config.json');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('createMcpClient', () => {
    it('should create client from config file successfully', async () => {
      // Arrange
      (MCPClient.fromConfigFile as any).mockReturnValue(mockClient);

      // Act
      const result = await createMcpClient();

      // Assert
      expect(result).toBeDefined();
      expect(result.client).toBe(mockClient);
      expect(result.closeAll).toBeInstanceOf(Function);
      expect(MCPClient.fromConfigFile).toHaveBeenCalledWith('/test/server/mcp-config.json');
      expect(path.join).toHaveBeenCalledWith('/test/server', 'mcp-config.json');
    });

    it('should handle cleanup properly', async () => {
      // Arrange
      (MCPClient.fromConfigFile as any).mockReturnValue(mockClient);
      mockClient.closeAllSessions.mockResolvedValue(undefined);

      // Act
      const { closeAll } = await createMcpClient();
      await closeAll();

      // Assert
      expect(mockClient.closeAllSessions).toHaveBeenCalledOnce();
    });

    it('should handle cleanup errors gracefully', async () => {
      // Arrange
      (MCPClient.fromConfigFile as any).mockReturnValue(mockClient);
      const error = new Error('Cleanup failed');
      mockClient.closeAllSessions.mockRejectedValue(error);
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      const { closeAll } = await createMcpClient();
      await expect(closeAll()).resolves.not.toThrow();

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('Error closing MCP client sessions:', error);
      consoleSpy.mockRestore();
    });

    it('should throw error when client creation fails', async () => {
      // Arrange
      const error = new Error('Config file not found');
      (MCPClient.fromConfigFile as any).mockImplementation(() => {
        throw error;
      });
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(createMcpClient()).rejects.toThrow('MCP client initialization failed: Config file not found');
      expect(consoleSpy).toHaveBeenCalledWith('Failed to create MCP client:', error);
      consoleSpy.mockRestore();
    });
  });

  describe('createMcpClientFromConfig', () => {
    const mockConfig = {
      mcpServers: {
        'test-server': {
          command: 'test',
          args: ['--test']
        }
      }
    };

    it('should create client from config object successfully', async () => {
      // Arrange
      (MCPClient.fromDict as any).mockReturnValue(mockClient);

      // Act
      const result = await createMcpClientFromConfig(mockConfig);

      // Assert
      expect(result).toBeDefined();
      expect(result.client).toBe(mockClient);
      expect(result.closeAll).toBeInstanceOf(Function);
      expect(MCPClient.fromDict).toHaveBeenCalledWith(mockConfig);
    });

    it('should handle cleanup properly', async () => {
      // Arrange
      (MCPClient.fromDict as any).mockReturnValue(mockClient);
      mockClient.closeAllSessions.mockResolvedValue(undefined);

      // Act
      const { closeAll } = await createMcpClientFromConfig(mockConfig);
      await closeAll();

      // Assert
      expect(mockClient.closeAllSessions).toHaveBeenCalledOnce();
    });

    it('should throw error when client creation from config fails', async () => {
      // Arrange
      const error = new Error('Invalid config');
      (MCPClient.fromDict as any).mockImplementation(() => {
        throw error;
      });
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(createMcpClientFromConfig(mockConfig)).rejects.toThrow('MCP client initialization failed: Invalid config');
      expect(consoleSpy).toHaveBeenCalledWith('Failed to create MCP client from config:', error);
      consoleSpy.mockRestore();
    });
  });
});
