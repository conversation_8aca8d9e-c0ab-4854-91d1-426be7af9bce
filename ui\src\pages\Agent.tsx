import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Bot, 
  Send, 
  Square, 
  Loader2, 
  Settings, 
  MessageSquare,
  Zap,
  AlertCircle,
  CheckCircle,
  Clock,
  Wrench
} from 'lucide-react';
import { runAgent } from '@/lib/serverComm';

interface AgentEvent {
  type: 'token' | 'tool_call' | 'tool_result' | 'error' | 'complete' | 'step' | 'thinking';
  message?: string;
  tool?: string;
  toolArgs?: any;
  toolResult?: any;
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
  requestId: string;
  timestamp: string;
  stepNumber?: number;
  totalSteps?: number;
}

interface AgentOptions {
  maxSteps: number;
  modelName: string;
  additionalDisallowedTools: string[];
}

export function Agent() {
  const [prompt, setPrompt] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [events, setEvents] = useState<AgentEvent[]>([]);
  const [currentResponse, setCurrentResponse] = useState('');
  const [options, setOptions] = useState<AgentOptions>({
    maxSteps: 10,
    modelName: 'gpt-4o',
    additionalDisallowedTools: []
  });
  const [showSettings, setShowSettings] = useState(false);
  const [newTool, setNewTool] = useState('');
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const eventsEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    eventsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [events, currentResponse]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isRunning) return;

    setIsRunning(true);
    setEvents([]);
    setCurrentResponse('');
    
    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      await runAgent(
        {
          prompt: prompt.trim(),
          options: {
            maxSteps: options.maxSteps,
            modelName: options.modelName,
            additionalDisallowedTools: options.additionalDisallowedTools.length > 0 
              ? options.additionalDisallowedTools 
              : undefined
          }
        },
        (event: AgentEvent) => {
          setEvents(prev => [...prev, event]);
          
          // Accumulate response tokens
          if (event.type === 'token' && event.message) {
            setCurrentResponse(prev => prev + event.message);
          }
        },
        abortControllerRef.current.signal
      );
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        setEvents(prev => [...prev, {
          type: 'error',
          error: {
            message: error.message,
            code: 'CLIENT_ERROR'
          },
          requestId: 'client-error',
          timestamp: new Date().toISOString()
        }]);
      }
    } finally {
      setIsRunning(false);
      abortControllerRef.current = null;
    }
  };

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsRunning(false);
    }
  };

  const addDisallowedTool = () => {
    if (newTool.trim() && !options.additionalDisallowedTools.includes(newTool.trim())) {
      setOptions(prev => ({
        ...prev,
        additionalDisallowedTools: [...prev.additionalDisallowedTools, newTool.trim()]
      }));
      setNewTool('');
    }
  };

  const removeDisallowedTool = (tool: string) => {
    setOptions(prev => ({
      ...prev,
      additionalDisallowedTools: prev.additionalDisallowedTools.filter(t => t !== tool)
    }));
  };

  const examplePrompts = [
    "Analyze the current trends in artificial intelligence and their potential impact on software development",
    "Research the latest developments in quantum computing and explain their practical applications",
    "Compare the performance characteristics of different database technologies for high-traffic applications",
    "Investigate the current state of renewable energy adoption globally and identify key challenges",
    "Analyze the cybersecurity landscape for small businesses and recommend best practices"
  ];

  const useExamplePrompt = (prompt: string) => {
    setPrompt(prompt);
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'token': return <MessageSquare className="w-4 h-4 text-blue-500" />;
      case 'tool_call': return <Wrench className="w-4 h-4 text-orange-500" />;
      case 'tool_result': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'complete': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'step': return <Zap className="w-4 h-4 text-purple-500" />;
      case 'thinking': return <Clock className="w-4 h-4 text-gray-500" />;
      default: return <MessageSquare className="w-4 h-4 text-gray-500" />;
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'token': return 'bg-blue-50 border-blue-200';
      case 'tool_call': return 'bg-orange-50 border-orange-200';
      case 'tool_result': return 'bg-green-50 border-green-200';
      case 'error': return 'bg-red-50 border-red-200';
      case 'complete': return 'bg-green-50 border-green-300';
      case 'step': return 'bg-purple-50 border-purple-200';
      case 'thinking': return 'bg-gray-50 border-gray-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="flex items-center gap-3 mb-6">
        <Bot className="w-8 h-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold">MCP Analysis Companion</h1>
          <p className="text-muted-foreground">
            AI-powered analysis agent with access to multiple tools via Model Context Protocol
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Input Section */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Agent Prompt</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSettings(!showSettings)}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </Button>
              </CardTitle>
              <CardDescription>
                Enter your analysis request. The agent can use web search, data analysis, and other tools.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <Textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Example: Analyze the current trends in artificial intelligence and their potential impact on software development..."
                  className="min-h-[120px] resize-none"
                  disabled={isRunning}
                />

                {!prompt && (
                  <div className="space-y-2">
                    <Label className="text-sm text-muted-foreground">Example prompts:</Label>
                    <div className="grid gap-2">
                      {examplePrompts.map((example, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          className="h-auto p-3 text-left justify-start text-wrap"
                          onClick={() => useExamplePrompt(example)}
                          disabled={isRunning}
                        >
                          <span className="text-sm">{example}</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
                
                {showSettings && (
                  <Card className="border-dashed">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Agent Configuration</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="maxSteps">Max Steps</Label>
                          <Input
                            id="maxSteps"
                            type="number"
                            min="1"
                            max="20"
                            value={options.maxSteps}
                            onChange={(e) => setOptions(prev => ({
                              ...prev,
                              maxSteps: parseInt(e.target.value) || 10
                            }))}
                          />
                        </div>
                        <div>
                          <Label htmlFor="modelName">Model</Label>
                          <Input
                            id="modelName"
                            value={options.modelName}
                            onChange={(e) => setOptions(prev => ({
                              ...prev,
                              modelName: e.target.value
                            }))}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label>Additional Disallowed Tools</Label>
                        <div className="flex gap-2 mt-1">
                          <Input
                            value={newTool}
                            onChange={(e) => setNewTool(e.target.value)}
                            placeholder="Enter tool name"
                            onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addDisallowedTool())}
                          />
                          <Button type="button" onClick={addDisallowedTool} size="sm">
                            Add
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          <Badge variant="secondary">file_system (default)</Badge>
                          <Badge variant="secondary">network (default)</Badge>
                          {options.additionalDisallowedTools.map(tool => (
                            <Badge 
                              key={tool} 
                              variant="outline" 
                              className="cursor-pointer"
                              onClick={() => removeDisallowedTool(tool)}
                            >
                              {tool} ×
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                <div className="flex gap-2">
                  <Button 
                    type="submit" 
                    disabled={!prompt.trim() || isRunning}
                    className="flex-1"
                  >
                    {isRunning ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Running...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Run Agent
                      </>
                    )}
                  </Button>
                  
                  {isRunning && (
                    <Button 
                      type="button" 
                      variant="destructive" 
                      onClick={handleStop}
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Response Section */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Agent Response
                {isRunning && (
                  <Badge variant="secondary" className="animate-pulse">
                    <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                    Running
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Real-time streaming response from the AI agent
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="min-h-[200px] max-h-[400px] overflow-y-auto p-3 bg-muted/30 rounded-md">
                {currentResponse ? (
                  <div className="whitespace-pre-wrap text-sm">
                    {currentResponse}
                    {isRunning && <span className="animate-pulse">|</span>}
                  </div>
                ) : (
                  <div className="text-muted-foreground text-sm text-center py-8">
                    Agent response will appear here...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Agent Capabilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Web search & research</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Data analysis</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Multi-step reasoning</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <AlertCircle className="w-4 h-4 text-orange-500" />
                <span>File system disabled</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <AlertCircle className="w-4 h-4 text-orange-500" />
                <span>Network tools disabled</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Events Log */}
      {events.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Agent Events Log</CardTitle>
            <CardDescription>
              Detailed log of agent actions and tool usage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="max-h-[500px] overflow-y-auto space-y-2">
              {events.map((event, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${getEventColor(event.type)}`}
                >
                  <div className="flex items-start gap-3">
                    {getEventIcon(event.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {event.type}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(event.timestamp).toLocaleTimeString()}
                        </span>
                        {event.stepNumber && (
                          <Badge variant="secondary" className="text-xs">
                            Step {event.stepNumber}
                          </Badge>
                        )}
                      </div>
                      
                      {event.message && (
                        <p className="text-sm mb-2">{event.message}</p>
                      )}
                      
                      {event.tool && (
                        <div className="text-xs">
                          <strong>Tool:</strong> {event.tool}
                          {event.toolArgs && (
                            <details className="mt-1">
                              <summary className="cursor-pointer text-muted-foreground">
                                Arguments
                              </summary>
                              <pre className="mt-1 p-2 bg-background rounded text-xs overflow-x-auto">
                                {JSON.stringify(event.toolArgs, null, 2)}
                              </pre>
                            </details>
                          )}
                          {event.toolResult && (
                            <details className="mt-1">
                              <summary className="cursor-pointer text-muted-foreground">
                                Result
                              </summary>
                              <pre className="mt-1 p-2 bg-background rounded text-xs overflow-x-auto">
                                {JSON.stringify(event.toolResult, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      )}
                      
                      {event.error && (
                        <div className="text-xs">
                          <strong className="text-red-600">Error:</strong> {event.error.message}
                          {event.error.code && (
                            <span className="text-muted-foreground"> ({event.error.code})</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={eventsEndRef} />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
