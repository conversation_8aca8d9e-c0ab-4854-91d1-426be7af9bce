## Research Report – mcp-use-ts (Unified MCP Client Library)

**Date:** 2025-08-11  
**Author:** Cursor Agent  
**Target Version:** mcp-use latest on npm (as of 2025-08-11)

---

### 1. Overview
`mcp-use-ts` (published as `mcp-use`) is a TypeScript library that connects LangChain.js-compatible LLMs (OpenAI, Anthropic, etc.) to MCP servers (Model Context Protocol). It enables building agents that dynamically discover and invoke tools from one or more MCP servers over HTTP/SSE or local processes.

---

### 2. Official Documentation Review
- **Docs URL:** `https://docs.mcp-use.io` (library docs)  
- **Repo (library):** `https://github.com/mcp-use/mcp-use-ts`  
- **Model Context Protocol (org):** `https://github.com/modelcontextprotocol`

- **Key API References:**
  - `MC<PERSON>lient`
    - Create from dictionary or JSON file via `MCPClient.fromDict(config)` and `MCPClient.fromConfigFile(path)`
    - Manages sessions to multiple MCP servers and lifecycle (`closeAllSessions()`)
  - `MCPAgent`
    - Orchestrates an LLM with MCP tools
    - Methods: `run(query)`, `stream(query)`, `streamEvents(query)`
    - Options: `maxSteps`, `disallowedTools`, `useServerManager`
  - AI SDK helpers: `streamEventsToAISDK`, `streamEventsToAISDKWithTools`, `createReadableStreamFromGenerator`

---

### 3. Example Implementations
Source: library examples folder.

| Example Name | Source Link | Notes |
|--------------|-------------|-------|
| AI SDK integration | `https://github.com/mcp-use/mcp-use-ts/blob/main/examples/ai_sdk_example.ts` | Streams `streamEvents` to Vercel AI SDK for UI updates. |
| Multi-server | `https://github.com/mcp-use/mcp-use-ts/blob/main/examples/multi_server_example.ts` | Configure multiple MCP servers; dynamic selection. |
| HTTP server | `https://github.com/mcp-use/mcp-use-ts/blob/main/examples/http_example.ts` | Connect over HTTP/SSE to remote servers. |
| Everything server | `https://github.com/mcp-use/mcp-use-ts/blob/main/examples/mcp_everything.ts` | End-to-end tool discovery/invocation. |
| Streaming | `https://github.com/mcp-use/mcp-use-ts/blob/main/examples/stream_example.ts` | Demonstrates `stream()` and `streamEvents()` behaviors. |
| Structured output | `https://github.com/mcp-use/mcp-use-ts/blob/main/examples/structured_output.ts` | Use structured outputs with tool calls. |
| Add server tool | `https://github.com/mcp-use/mcp-use-ts/blob/main/examples/add_server_tool.ts` | Programmatically add tools/servers. |

---

### 4. Best Practices
- **Modular configuration:** keep MCP server definitions in a JSON (e.g., `mcp-config.json`) and load with `MCPClient.fromConfigFile`. Simplifies multi-env setups.
- **Dynamic server selection:** enable `useServerManager` on `MCPAgent` when routing across multiple servers.
- **Explicit tool allow/deny:** use `disallowedTools` to disable risky capabilities (`file_system`, `network`) by default.
- **Streaming for UX:** prefer `streamEvents()` for granular events and token streaming; it integrates cleanly with frontends.
- **Session hygiene:** always `await client.closeAllSessions()` in API handlers to avoid resource leaks.
- **Environment parity:** pin Node.js ≥ 22 and align LangChain/OpenAI client versions across environments.

---

### 5. Common Pitfalls & Gotchas
- **Node version:** features and examples target Node 22+. Older runtimes can break streams or subprocess handling.
- **Remote HTTP/SSE nuances:** ensure CORS, TLS, and long-lived SSE connections are allowed by the MCP server infra.
- **Tool naming collisions:** when combining multiple servers, tool names may overlap. Add namespacing or selection logic.
- **Over-permissive tools:** exposing filesystem/network tools beyond need increases risk; restrict at agent level.

---

### 6. Security Considerations
- **Least privilege:** deny high-risk tools by default via `disallowedTools`. Allow only what a task needs.
- **Secrets handling:** store API keys (`OPENAI_API_KEY`, etc.) in env vars; never serialize them into prompts/tool inputs.
- **Remote server trust:** only connect to HTTPS endpoints you control or trust. Validate server origins and certificates.
- **Prompt injection defenses:** validate/normalize tool inputs; consider allowlists and schema validation before invoking tools.
- **Session isolation:** do not reuse sessions between users/tenants; tear down sessions after each request if multi-tenant.

---

### 7. Implementation Plan Notes
- **Target:** Build an analysis companion agent using OpenAI models and multiple MCP tools, including your `hustle-http` remote.

- **Baseline config (provided):**
```json
{
  "mcpServers": {
    "hustle-http": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://agenthustle.myagent.sh/mcp"
      ]
    }
  }
}
```

- **Agent wiring (high level):**
  1) `const client = MCPClient.fromDict(config)`
  2) `const llm = new ChatOpenAI({ modelName: 'gpt-4o' })` (or preferred OpenAI model)
  3) `const agent = new MCPAgent({ llm, client, maxSteps: 10, disallowedTools: ['file_system','network'] })`
  4) Use `agent.streamEvents()` for UI; convert via `streamEventsToAISDK` when using Vercel AI SDK.
  5) On request end, `await client.closeAllSessions()`.

- **Multi-MCP expansion:** Extend `config.mcpServers` with additional remote/local MCPs (e.g., `@modelcontextprotocol/server-everything`, Playwright MCP, filesystem MCP) and enable `useServerManager`.

---

### 8. References
- Library Docs: `https://docs.mcp-use.io`
- Library Repo: `https://github.com/mcp-use/mcp-use-ts`
- Examples Folder: `https://github.com/mcp-use/mcp-use-ts/tree/main/examples`
- Remote bridge (mcp-remote): `https://github.com/geelen/mcp-remote`
- Model Context Protocol (org): `https://github.com/modelcontextprotocol`
- OpenAI API (Node): `https://platform.openai.com/docs/`


