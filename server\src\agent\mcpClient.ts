import { MC<PERSON>lient } from 'mcp-use';
import path from 'path';

/**
 * MCP Client factory with session management
 * Provides a factory pattern for creating MCPClient instances with proper resource cleanup
 */

export interface MCPClientWrapper {
  client: MCPClient;
  closeAll: () => Promise<void>;
}

/**
 * Create an MCP client from the configuration file
 * @returns Promise resolving to client wrapper with cleanup function
 */
export async function createMcpClient(): Promise<MCPClientWrapper> {
  try {
    // Load configuration from the mcp-config.json file in the server root
    const configPath = path.join(process.cwd(), 'mcp-config.json');
    
    // Create client from configuration file
    const client = MCPClient.fromConfigFile(configPath);
    
    return {
      client,
      closeAll: async () => {
        try {
          await client.closeAllSessions();
        } catch (error) {
          console.error('Error closing MCP client sessions:', error);
          // Don't throw - we want cleanup to be best-effort
        }
      }
    };
  } catch (error) {
    console.error('Failed to create MCP client:', error);
    throw new Error(`MCP client initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create an MCP client from a configuration object
 * @param config MCP configuration object
 * @returns Promise resolving to client wrapper with cleanup function
 */
export async function createMcpClientFromConfig(config: any): Promise<MCPClientWrapper> {
  try {
    const client = MCPClient.fromDict(config);
    
    return {
      client,
      closeAll: async () => {
        try {
          await client.closeAllSessions();
        } catch (error) {
          console.error('Error closing MCP client sessions:', error);
          // Don't throw - we want cleanup to be best-effort
        }
      }
    };
  } catch (error) {
    console.error('Failed to create MCP client from config:', error);
    throw new Error(`MCP client initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
