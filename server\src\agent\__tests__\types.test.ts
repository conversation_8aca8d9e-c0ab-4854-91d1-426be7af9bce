import { describe, it, expect } from 'vitest';
import { validateAgentRunRequest, AgentRunRequest } from '../types';

describe('Agent Types Validation', () => {
  describe('validateAgentRunRequest', () => {
    it('should validate a minimal valid request', () => {
      // Arrange
      const body = {
        prompt: 'Hello, world!'
      };

      // Act
      const result = validateAgentRunRequest(body);

      // Assert
      expect(result).toEqual({
        prompt: 'Hello, world!'
      });
    });

    it('should validate a complete valid request', () => {
      // Arrange
      const body = {
        prompt: '  Analyze this data  ',
        options: {
          maxSteps: 15,
          modelName: 'gpt-4',
          additionalDisallowedTools: ['dangerous_tool', 'risky_tool']
        }
      };

      // Act
      const result = validateAgentRunRequest(body);

      // Assert
      expect(result).toEqual({
        prompt: 'Analyze this data',
        options: {
          maxSteps: 15,
          modelName: 'gpt-4',
          additionalDisallowedTools: ['dangerous_tool', 'risky_tool']
        }
      });
    });

    it('should trim whitespace from prompt', () => {
      // Arrange
      const body = {
        prompt: '   Test prompt   '
      };

      // Act
      const result = validateAgentRunRequest(body);

      // Assert
      expect(result.prompt).toBe('Test prompt');
    });

    it('should filter out empty strings from additionalDisallowedTools', () => {
      // Arrange
      const body = {
        prompt: 'Test',
        options: {
          additionalDisallowedTools: ['tool1', '', '  ', 'tool2', null, undefined]
        }
      };

      // Act
      const result = validateAgentRunRequest(body);

      // Assert
      expect(result.options?.additionalDisallowedTools).toEqual(['tool1', 'tool2']);
    });

    describe('error cases', () => {
      it('should throw error for null body', () => {
        expect(() => validateAgentRunRequest(null)).toThrow('Request body must be a valid JSON object');
      });

      it('should throw error for undefined body', () => {
        expect(() => validateAgentRunRequest(undefined)).toThrow('Request body must be a valid JSON object');
      });

      it('should throw error for non-object body', () => {
        expect(() => validateAgentRunRequest('string')).toThrow('Request body must be a valid JSON object');
        expect(() => validateAgentRunRequest(123)).toThrow('Request body must be a valid JSON object');
        expect(() => validateAgentRunRequest([])).toThrow('Request body must be a valid JSON object');
      });

      it('should throw error for missing prompt', () => {
        expect(() => validateAgentRunRequest({})).toThrow('Prompt is required and must be a non-empty string');
      });

      it('should throw error for null prompt', () => {
        expect(() => validateAgentRunRequest({ prompt: null })).toThrow('Prompt is required and must be a non-empty string');
      });

      it('should throw error for non-string prompt', () => {
        expect(() => validateAgentRunRequest({ prompt: 123 })).toThrow('Prompt is required and must be a non-empty string');
        expect(() => validateAgentRunRequest({ prompt: {} })).toThrow('Prompt is required and must be a non-empty string');
      });

      it('should throw error for empty prompt', () => {
        expect(() => validateAgentRunRequest({ prompt: '' })).toThrow('Prompt is required and must be a non-empty string');
        expect(() => validateAgentRunRequest({ prompt: '   ' })).toThrow('Prompt is required and must be a non-empty string');
      });

      it('should throw error for prompt too long', () => {
        const longPrompt = 'a'.repeat(10001);
        expect(() => validateAgentRunRequest({ prompt: longPrompt })).toThrow('Prompt must be less than 10,000 characters');
      });

      it('should throw error for invalid maxSteps', () => {
        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { maxSteps: 0 } 
        })).toThrow('maxSteps must be a number between 1 and 20');

        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { maxSteps: 21 } 
        })).toThrow('maxSteps must be a number between 1 and 20');

        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { maxSteps: 'invalid' } 
        })).toThrow('maxSteps must be a number between 1 and 20');

        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { maxSteps: -5 } 
        })).toThrow('maxSteps must be a number between 1 and 20');
      });

      it('should throw error for invalid modelName', () => {
        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { modelName: '' } 
        })).toThrow('modelName must be a non-empty string');

        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { modelName: '   ' } 
        })).toThrow('modelName must be a non-empty string');

        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { modelName: 123 } 
        })).toThrow('modelName must be a non-empty string');
      });

      it('should throw error for invalid additionalDisallowedTools', () => {
        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { additionalDisallowedTools: 'not-array' } 
        })).toThrow('additionalDisallowedTools must be an array');

        expect(() => validateAgentRunRequest({ 
          prompt: 'test', 
          options: { additionalDisallowedTools: 123 } 
        })).toThrow('additionalDisallowedTools must be an array');
      });
    });

    describe('edge cases', () => {
      it('should handle valid maxSteps boundary values', () => {
        const result1 = validateAgentRunRequest({ prompt: 'test', options: { maxSteps: 1 } });
        expect(result1.options?.maxSteps).toBe(1);

        const result2 = validateAgentRunRequest({ prompt: 'test', options: { maxSteps: 20 } });
        expect(result2.options?.maxSteps).toBe(20);
      });

      it('should handle empty options object', () => {
        const result = validateAgentRunRequest({ prompt: 'test', options: {} });
        expect(result).toEqual({ prompt: 'test', options: {} });
      });

      it('should handle undefined options', () => {
        const result = validateAgentRunRequest({ prompt: 'test', options: undefined });
        expect(result).toEqual({ prompt: 'test' });
      });

      it('should trim modelName whitespace', () => {
        const result = validateAgentRunRequest({ 
          prompt: 'test', 
          options: { modelName: '  gpt-4  ' } 
        });
        expect(result.options?.modelName).toBe('gpt-4');
      });

      it('should handle empty additionalDisallowedTools array', () => {
        const result = validateAgentRunRequest({ 
          prompt: 'test', 
          options: { additionalDisallowedTools: [] } 
        });
        expect(result.options?.additionalDisallowedTools).toEqual([]);
      });
    });
  });
});
