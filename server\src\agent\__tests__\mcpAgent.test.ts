import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createMcpAgent, createMcpAgentWithLLM } from '../mcpAgent';
import { MCPAgent, MCPClient } from 'mcp-use';
import { ChatOpenAI } from '@langchain/openai';

// Mock the dependencies
vi.mock('mcp-use', () => ({
  MCPAgent: vi.fn(),
  MCPClient: vi.fn()
}));

vi.mock('@langchain/openai', () => ({
  ChatOpenAI: vi.fn()
}));

vi.mock('../../lib/env', () => ({
  getOpenAIApiKey: vi.fn()
}));

describe('MCP Agent Factory', () => {
  const mockClient = {} as MCPClient;
  const mockLLM = {} as ChatOpenAI;
  const mockAgent = {} as MCPAgent;

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock environment
    const { getOpenAIApiKey } = require('../../lib/env');
    getOpenAIApiKey.mockReturnValue('test-api-key');
    
    // Mock constructors
    (ChatOpenAI as any).mockImplementation(() => mockLLM);
    (MCPAgent as any).mockImplementation(() => mockAgent);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('createMcpAgent', () => {
    it('should create agent with default options', async () => {
      // Act
      const result = await createMcpAgent({ client: mockClient });

      // Assert
      expect(result).toBe(mockAgent);
      expect(ChatOpenAI).toHaveBeenCalledWith({
        modelName: 'gpt-4o',
        openAIApiKey: 'test-api-key',
        temperature: 0.1,
        maxTokens: 4000
      });
      expect(MCPAgent).toHaveBeenCalledWith({
        llm: mockLLM,
        client: mockClient,
        maxSteps: 10,
        disallowedTools: ['file_system', 'network'],
        useServerManager: false
      });
    });

    it('should create agent with custom options', async () => {
      // Act
      const result = await createMcpAgent({
        client: mockClient,
        modelName: 'gpt-3.5-turbo',
        maxSteps: 5,
        disallowedTools: ['file_system', 'network', 'custom_tool'],
        useServerManager: true
      });

      // Assert
      expect(result).toBe(mockAgent);
      expect(ChatOpenAI).toHaveBeenCalledWith({
        modelName: 'gpt-3.5-turbo',
        openAIApiKey: 'test-api-key',
        temperature: 0.1,
        maxTokens: 4000
      });
      expect(MCPAgent).toHaveBeenCalledWith({
        llm: mockLLM,
        client: mockClient,
        maxSteps: 5,
        disallowedTools: ['file_system', 'network', 'custom_tool'],
        useServerManager: true
      });
    });

    it('should throw error when OpenAI API key is missing', async () => {
      // Arrange
      const { getOpenAIApiKey } = require('../../lib/env');
      getOpenAIApiKey.mockImplementation(() => {
        throw new Error('Required environment variable OPENAI_API_KEY is not set');
      });
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(createMcpAgent({ client: mockClient })).rejects.toThrow(
        'MCP agent initialization failed: Required environment variable OPENAI_API_KEY is not set'
      );
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should throw error when ChatOpenAI creation fails', async () => {
      // Arrange
      const error = new Error('OpenAI initialization failed');
      (ChatOpenAI as any).mockImplementation(() => {
        throw error;
      });
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(createMcpAgent({ client: mockClient })).rejects.toThrow(
        'MCP agent initialization failed: OpenAI initialization failed'
      );
      expect(consoleSpy).toHaveBeenCalledWith('Failed to create MCP agent:', error);
      consoleSpy.mockRestore();
    });

    it('should throw error when MCPAgent creation fails', async () => {
      // Arrange
      const error = new Error('Agent initialization failed');
      (MCPAgent as any).mockImplementation(() => {
        throw error;
      });
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(createMcpAgent({ client: mockClient })).rejects.toThrow(
        'MCP agent initialization failed: Agent initialization failed'
      );
      expect(consoleSpy).toHaveBeenCalledWith('Failed to create MCP agent:', error);
      consoleSpy.mockRestore();
    });
  });

  describe('createMcpAgentWithLLM', () => {
    it('should create agent with custom LLM and default options', async () => {
      // Act
      const result = await createMcpAgentWithLLM(mockClient, mockLLM);

      // Assert
      expect(result).toBe(mockAgent);
      expect(MCPAgent).toHaveBeenCalledWith({
        llm: mockLLM,
        client: mockClient,
        maxSteps: 10,
        disallowedTools: ['file_system', 'network'],
        useServerManager: false
      });
    });

    it('should create agent with custom LLM and custom options', async () => {
      // Act
      const result = await createMcpAgentWithLLM(mockClient, mockLLM, {
        maxSteps: 15,
        disallowedTools: ['custom_tool'],
        useServerManager: true
      });

      // Assert
      expect(result).toBe(mockAgent);
      expect(MCPAgent).toHaveBeenCalledWith({
        llm: mockLLM,
        client: mockClient,
        maxSteps: 15,
        disallowedTools: ['custom_tool'],
        useServerManager: true
      });
    });

    it('should throw error when MCPAgent creation fails', async () => {
      // Arrange
      const error = new Error('Agent initialization failed');
      (MCPAgent as any).mockImplementation(() => {
        throw error;
      });
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(createMcpAgentWithLLM(mockClient, mockLLM)).rejects.toThrow(
        'MCP agent initialization failed: Agent initialization failed'
      );
      expect(consoleSpy).toHaveBeenCalledWith('Failed to create MCP agent with custom LLM:', error);
      consoleSpy.mockRestore();
    });
  });
});
