# Design System Implementation Guide

## 🎯 Overview

This document provides a comprehensive guide to using the design system in the Volo App Template. The design system ensures visual consistency, improves developer experience, and maintains scalable design patterns across the application.

## 🎨 Design Tokens

### Colors

The design system uses semantic color tokens that automatically adapt to light and dark themes:

```css
/* Primary Brand Colors */
--primary: #6e56cf (Light) / #a48fff (Dark)
--secondary: #e4dfff (Light) / #2d2b55 (Dark)
--accent: #d8e6ff (Light) / #303060 (Dark)

/* Semantic Colors */
--destructive: #ff5470
--background: #f5f5ff (Light) / #0f0f1a (Dark)
--foreground: #2a2a4a (Light) / #e2e2f5 (Dark)
```

### Typography

The typography system follows an 8pt grid with semantic classes:

```css
/* Font Families */
--font-family-serif: 'DM Serif Display', serif
--font-family-mono: 'Roboto Mono', monospace
--font-family-sans: system-ui, sans-serif

/* Typography Scale */
.text-xs    /* 12px/16px - Labels/Captions */
.text-sm    /* 14px/20px - UI Labels */
.text-base  /* 16px/24px - Body Text */
.text-md    /* 18px/28px - Paragraphs */
.text-lg    /* 20px/32px - H5 */
.text-xl    /* 24px/40px - H4 */
.text-2xl   /* 32px/48px - H3 */
.text-3xl   /* 40px/56px - H2 */
.text-4xl   /* 48px/64px - H1/Hero */
```

### Semantic Typography Classes

```css
.h1, .h2    /* Serif headings for hero content */
.h3, .h4, .h5  /* Sans-serif headings for UI */
.body-text  /* Standard body text */
.label      /* Form labels and UI text */
.caption    /* Small descriptive text */
```

### Spacing System

Based on an 8pt grid for consistent rhythm:

```css
--space-1: 8px    --space-6: 48px
--space-2: 16px   --space-7: 56px
--space-3: 24px   --space-8: 64px
--space-4: 32px   --space-9: 72px
--space-5: 40px   --space-10: 80px
```

### Shadows

Consistent elevation system:

```css
--shadow-xs: Subtle shadow for cards
--shadow-sm: Small shadow for buttons
--shadow-md: Medium shadow for modals
--shadow-lg: Large shadow for dropdowns
--shadow-xl: Extra large for overlays
```

## 🛠️ Usage Guide

### Using Typography

```tsx
// Semantic headings
<h1 className="h1">Hero Title</h1>
<h2 className="h2">Section Title</h2>
<h3 className="h3">Subsection</h3>

// Body text
<p className="body-text">Standard paragraph text</p>
<span className="label">Form label</span>
<small className="caption">Helper text</small>

// Font families
<h1 className="font-serif">Serif heading</h1>
<code className="font-mono">Code snippet</code>
```

### Using Colors

```tsx
// Text colors
<span className="text-primary">Primary text</span>
<span className="text-muted-foreground">Muted text</span>

// Background colors
<div className="bg-primary text-primary-foreground">
  Primary background
</div>
<div className="bg-card text-card-foreground">
  Card background
</div>
```

### Using Spacing

```tsx
// Padding and margins using design system
<div className="p-4 m-2">Content with system spacing</div>
<div className="space-y-3">Vertical spacing between children</div>
<div className="gap-4">Grid/flex gap</div>
```

### Using Shadows

```tsx
// Card with elevation
<div className="shadow-sm hover:shadow-md transition-shadow">
  Interactive card
</div>

// Modal with strong elevation
<div className="shadow-xl">Modal content</div>
```

## 🧩 Component Patterns

### Button Variants

```tsx
import { Button } from '@/components/ui/button';

<Button variant="default">Primary Action</Button>
<Button variant="secondary">Secondary Action</Button>
<Button variant="outline">Outline Button</Button>
<Button variant="ghost">Ghost Button</Button>
<Button variant="destructive">Delete Action</Button>
```

### Card Layouts

```tsx
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description text</CardDescription>
  </CardHeader>
  <CardContent>
    <p className="body-text">Card content</p>
  </CardContent>
</Card>
```

### Form Elements

```tsx
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

<div className="space-y-2">
  <Label className="label">Email Address</Label>
  <Input type="email" placeholder="Enter your email" />
  <p className="caption">We'll never share your email</p>
</div>
```

## 📚 Design System Utilities

Import the design system utilities for programmatic access:

```tsx
import { designSystem, typography, colors, spacing } from '@/lib/design-system';

// Use semantic classes
const titleClass = designSystem.text('h1');
const primaryColor = designSystem.color('primary');
const cardSpacing = designSystem.space(4);

// Combine multiple classes
const combinedClass = designSystem.combine(
  typography.semantic.h2,
  colors.primary,
  spacing[3]
);
```

## 🎯 Best Practices

### Do's ✅

- Use semantic color tokens (`text-primary`, `bg-card`)
- Use typography scale classes (`h1`, `h2`, `body-text`)
- Follow 8pt spacing grid (`space-1` through `space-10`)
- Use design system utilities for consistency
- Test both light and dark themes

### Don'ts ❌

- Don't use hardcoded colors (`text-blue-500`)
- Don't use arbitrary font sizes (`text-[17px]`)
- Don't use non-grid spacing (`p-[13px]`)
- Don't override design system tokens without reason
- Don't forget to test theme switching

## 🔧 Development Workflow

1. **Start with design tokens** - Use CSS variables and utility classes
2. **Use semantic classes** - Prefer `h1`, `body-text` over size classes
3. **Follow spacing grid** - Use `space-1` through `space-10`
4. **Test themes** - Verify both light and dark modes
5. **Use TypeScript utilities** - Import from `@/lib/design-system`

## 📱 Responsive Design

The design system includes responsive utilities:

```tsx
// Responsive typography
<h1 className="h1 md:text-4xl">Responsive heading</h1>

// Responsive spacing
<div className="p-4 md:p-6 lg:p-8">Responsive padding</div>

// Responsive layout
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  Responsive grid
</div>
```

## 🎨 Customization

To customize the design system:

1. Update tokens in `/my-app/design/desgn-system.md`
2. Modify CSS variables in `/my-app/ui/src/index.css`
3. Update Tailwind theme in the `@theme inline` section
4. Test changes across all components

## 📖 Resources

- **Design System File**: `/my-app/design/desgn-system.md`
- **CSS Implementation**: `/my-app/ui/src/index.css`
- **TypeScript Utilities**: `/my-app/ui/src/lib/design-system.ts`
- **Component Library**: `/my-app/ui/src/components/ui/`

---

**Last Updated**: 2025-01-08
**Version**: 1.0
**Maintainer**: Volo App Template Team
