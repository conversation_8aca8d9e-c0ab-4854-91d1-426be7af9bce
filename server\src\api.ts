import 'dotenv/config';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { authMiddleware } from './middleware/auth';
import { getDatabase, testDatabaseConnection } from './lib/db';
import { setEnvContext, clearEnvContext, getDatabaseUrl } from './lib/env';
import * as schema from './schema/users';
// import { createMcpClient } from './agent/mcpClient';
// import { createMcpAgent } from './agent/mcpAgent';
// import { validateAgentRunRequest, AgentEvent } from './agent/types';
// import { randomUUID } from 'crypto';

type Env = {
  RUNTIME?: string;
  [key: string]: any;
};

const app = new Hono<{ Bindings: Env }>();

// In Node.js environment, set environment context from process.env
if (typeof process !== 'undefined' && process.env) {
  setEnvContext(process.env);
}

// Environment context middleware - detect runtime using RUNTIME env var
app.use('*', async (c, next) => {
  if (c.env?.RUNTIME === 'cloudflare') {
    setEnvContext(c.env);
  }
  
  await next();
  // No need to clear context - env vars are the same for all requests
  // In fact, clearing the context would cause the env vars to potentially be unset for parallel requests
});

// Middleware
app.use('*', logger());
app.use('*', cors());

// Health check route - public
app.get('/', (c) => c.json({ status: 'ok', message: 'API is running' }));

// API routes
const api = new Hono();

// Public routes go here (if any)
api.get('/hello', (c) => {
  return c.json({
    message: 'Hello from Hono!',
  });
});

// Database test route - public for testing
api.get('/db-test', async (c) => {
  try {
    // Use external DB URL if available, otherwise use local PostgreSQL database server
    // Note: In development, the port is dynamically allocated by port-manager.js
    const defaultLocalConnection = process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5502/postgres';
    const dbUrl = getDatabaseUrl() || defaultLocalConnection;
    
    const db = await getDatabase(dbUrl);
    const isHealthy = await testDatabaseConnection();
    
    if (!isHealthy) {
      return c.json({
        error: 'Database connection is not healthy',
        timestamp: new Date().toISOString(),
      }, 500);
    }
    
    const result = await db.select().from(schema.users).limit(5);
    
    return c.json({
      message: 'Database connection successful!',
      users: result,
      connectionHealthy: isHealthy,
      usingLocalDatabase: !getDatabaseUrl(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Database test error:', error);
    return c.json({
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Agent routes - public for now, can be protected later
const agentRoutes = new Hono();

agentRoutes.post('/run', async (c) => {
  const requestId = randomUUID();
  const startTime = Date.now();

  try {
    // Parse and validate request body
    const body = await c.req.json();
    const request = validateAgentRunRequest(body);

    // Log request start (prompt length only for security)
    console.log(`[${requestId}] Agent request started`, {
      promptLength: request.prompt.length,
      maxSteps: request.options?.maxSteps,
      modelName: request.options?.modelName,
      timestamp: new Date().toISOString()
    });

    // Create MCP client and agent
    const { client, closeAll } = await createMcpClient();

    try {
      const agent = await createMcpAgent({
        client,
        modelName: request.options?.modelName,
        maxSteps: request.options?.maxSteps,
        disallowedTools: [
          'file_system',
          'network',
          ...(request.options?.additionalDisallowedTools || [])
        ]
      });

      // Set up streaming response
      return c.newResponse(
        new ReadableStream({
          async start(controller) {
            const encoder = new TextEncoder();
            let stepCount = 0;
            const toolsUsed = new Set<string>();

            try {
              // Stream agent events
              for await (const event of agent.streamEvents(request.prompt)) {
                stepCount++;

                // Type the event as any to handle the dynamic nature of MCP events
                const mcpEvent = event as any;

                // Track tool usage
                if (mcpEvent.type === 'tool_call' && mcpEvent.tool) {
                  toolsUsed.add(mcpEvent.tool);
                }

                // Create agent event
                const agentEvent: AgentEvent = {
                  type: mcpEvent.type || 'token',
                  message: mcpEvent.message || mcpEvent.text || mcpEvent.content,
                  tool: mcpEvent.tool,
                  toolArgs: mcpEvent.toolArgs || mcpEvent.args,
                  toolResult: mcpEvent.toolResult || mcpEvent.result,
                  requestId,
                  timestamp: new Date().toISOString(),
                  stepNumber: stepCount,
                  totalSteps: stepCount
                };

                // Send event as NDJSON
                const eventData = JSON.stringify(agentEvent) + '\n';
                controller.enqueue(encoder.encode(eventData));
              }

              // Send completion event
              const completionEvent: AgentEvent = {
                type: 'complete',
                message: 'Agent run completed successfully',
                requestId,
                timestamp: new Date().toISOString(),
                totalSteps: stepCount
              };

              const completionData = JSON.stringify(completionEvent) + '\n';
              controller.enqueue(encoder.encode(completionData));

              // Log completion
              const processingTime = Date.now() - startTime;
              console.log(`[${requestId}] Agent request completed`, {
                totalSteps: stepCount,
                toolsUsed: Array.from(toolsUsed),
                processingTimeMs: processingTime,
                timestamp: new Date().toISOString()
              });

            } catch (streamError) {
              console.error(`[${requestId}] Agent streaming error:`, streamError);

              // Send error event
              const errorEvent: AgentEvent = {
                type: 'error',
                error: {
                  message: streamError instanceof Error ? streamError.message : 'Unknown streaming error',
                  code: 'STREAMING_ERROR'
                },
                requestId,
                timestamp: new Date().toISOString()
              };

              const errorData = JSON.stringify(errorEvent) + '\n';
              controller.enqueue(encoder.encode(errorData));
            } finally {
              controller.close();
            }
          }
        }),
        {
          headers: {
            'Content-Type': 'application/x-ndjson',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
          }
        }
      );

    } finally {
      // Always cleanup MCP sessions
      await closeAll();
    }

  } catch (error) {
    console.error(`[${requestId}] Agent request error:`, error);

    // Return error response
    return c.json({
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      code: 'AGENT_ERROR',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// Mount agent routes
api.route('/agent', agentRoutes);

// Protected routes - require authentication
const protectedRoutes = new Hono();

protectedRoutes.use('*', authMiddleware);

protectedRoutes.get('/me', (c) => {
  const user = c.get('user');
  return c.json({
    user,
    message: 'You are authenticated!',
  });
});

// Mount the protected routes under /protected
api.route('/protected', protectedRoutes);

// Mount the API router
app.route('/api/v1', api);

export default app; 